import React, { useState, useCallback } from 'react';
import PirateButton from './components/PirateButton';
import { playRandomPirateSound } from './services/soundService';

function App() {
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  const handleButtonClick = useCallback(() => {
    playRandomPirateSound();
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
    }, 300);
  }, []);

  return (
    // Removed background classes to let the body's rainbow bg show through.
    // Updated text colors to white with drop-shadow for readability.
    <div className="h-screen w-screen flex flex-col items-center justify-center p-4 text-white overflow-hidden">
      <header className="text-center mb-8 md:mb-12">
        <h1 className="font-battlesbridge text-5xl md:text-7xl font-bold text-white drop-shadow-[0_3px_3px_rgba(0,0,0,0.9)]">
          Captain <PERSON><PERSON>'s Pirate Engine
        </h1>
      </header>
      
      <main className="flex-grow flex flex-col items-center justify-center">
        <PirateButton onClick={handleButtonClick} isAnimating={isAnimating} />
        <p className="font-battlesbridge text-xl md:text-2xl text-white mt-8 drop-shadow-[0_2px_2px_rgba(0,0,0,0.9)]">Push the skull!</p>
      </main>

      <footer className="font-battlesbridge text-white text-lg pb-4 drop-shadow-[0_2px_2px_rgba(0,0,0,0.9)]">
        A noisy treasure for little pirates.
      </footer>
    </div>
  );
}

export default App;