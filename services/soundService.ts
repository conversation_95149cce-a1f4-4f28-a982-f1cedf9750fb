// A list of pirate sound URLs sourced from the Pirate SFX Library soundboard.
// Using direct links that should work better with CORS
const pirateSounds = [
  'https://www.101soundboards.com/sounds/27278991-cannon-fire-single-shot-pirate-blast-explosion', // Cannon Fire Single Shot
  'https://www.101soundboards.com/sounds/27421388-cartoon-character-pirate-saying-ah-me-bones-are-tired', // Ah Me Bones Are Tired
  'https://www.101soundboards.com/sounds/27577787-cartoon-pirate-cheer-1', // Cartoon Pirate Cheer 1
  'https://www.101soundboards.com/sounds/27277437-cartoon-pirate-cheer-4', // Cartoon Pirate Cheer 4
  'https://www.101soundboards.com/sounds/27336936-coin-spin-single-pirate-gold-doubloon-wood-surface', // Coin Spin
  'https://www.101soundboards.com/sounds/27439723-coins-drop-multiple-pirate-gold-doubloon-wood-surface', // Coins Drop
  'https://www.101soundboards.com/sounds/27385961-fifty-year-old-man-says-in-a-pirate-voice-hang-em-from-the-yard-arm', // Hang Em From The Yard Arm
  'https://www.101soundboards.com/sounds/27359278-fifty-year-old-man-says-in-a-pirate-voice-heave-ho', // Heave Ho
  'https://www.101soundboards.com/sounds/27231181-human-element-male-old-pirate-ahoy-me-hearties-01', // Ahoy Me Hearties
  'https://www.101soundboards.com/sounds/27331586-human-element-male-old-pirate-argh-01', // Argh
  'https://www.101soundboards.com/sounds/27634204-human-element-male-pirate-land-ho-01', // Land Ho
  'https://www.101soundboards.com/sounds/27591884-human-voice-male-old-pirate-walk-the-plank-01', // Walk The Plank
  'https://www.101soundboards.com/sounds/27506407-pirate-belly-laugh', // Pirate Belly Laugh
  'https://www.101soundboards.com/sounds/27429037-man-says-in-a-pirate-voice-shiver-me-timbers', // Shiver Me Timbers
  'https://www.101soundboards.com/sounds/27300658-middle-aged-man-says-says-in-a-pirate-voice-aye-matie', // Aye Matie
  'https://www.101soundboards.com/sounds/27551195-middle-aged-man-says-says-in-a-pirate-voice-avast-ye-scallywags', // Avast Ye Scallywags
  'https://www.101soundboards.com/sounds/27617475-pirate-wheres-me-rum', // Where's Me Rum
  'https://www.101soundboards.com/sounds/27270061-pirate-saying-take-him-down-below', // Take Him Down Below
  'https://www.101soundboards.com/sounds/27469203-pirate-saying-try-again-laddy', // Try Again Laddy
  'https://www.101soundboards.com/sounds/27585217-twelve-year-old-boy-says-in-a-pirate-voice-aye-matie', // Young Aye Matie
];

// Simple audio playback with better error handling
export const playRandomPirateSound = () => {
  const randomIndex = Math.floor(Math.random() * pirateSounds.length);
  const soundPageUrl = pirateSounds[randomIndex];

  // Convert the sound page URL to direct MP3 URL
  const soundId = soundPageUrl.split('/sounds/')[1].split('-')[0];
  const directMp3Url = `https://www.101soundboards.com/storage/board_sounds/${soundId}.mp3`;

  console.log('Attempting to play:', directMp3Url);

  try {
    const audio = new Audio();

    // Set up event listeners
    audio.addEventListener('loadstart', () => {
      console.log('Audio loading started');
    });

    audio.addEventListener('canplay', () => {
      console.log('Audio can start playing');
    });

    audio.addEventListener('error', (e) => {
      console.error('Audio error:', e);
      // Fallback: show a message to the user
      const soundName = soundPageUrl.split('-').slice(1).join(' ').replace(/-/g, ' ');
      console.log(`Would play: ${soundName}`);

      // Create a visual feedback instead
      showSoundFeedback(soundName);
    });

    // Try to load and play the audio
    audio.src = directMp3Url;
    audio.volume = 0.7; // Set volume to 70%

    const playPromise = audio.play();

    if (playPromise !== undefined) {
      playPromise.then(() => {
        console.log('Audio playing successfully');
      }).catch(error => {
        console.warn('Play failed:', error);
        // Show visual feedback instead
        const soundName = soundPageUrl.split('-').slice(1).join(' ').replace(/-/g, ' ');
        showSoundFeedback(soundName);
      });
    }

  } catch (e) {
    console.error("Failed to create audio:", e);
    const soundName = soundPageUrl.split('-').slice(1).join(' ').replace(/-/g, ' ');
    showSoundFeedback(soundName);
  }
};

// Show visual feedback when audio can't play
const showSoundFeedback = (soundName: string) => {
  // Create a temporary visual element to show what sound would play
  const feedback = document.createElement('div');
  feedback.textContent = `🔊 ${soundName.charAt(0).toUpperCase() + soundName.slice(1)}`;
  feedback.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 10px;
    font-family: 'Battlesbridge', serif;
    font-size: 18px;
    z-index: 1000;
    pointer-events: none;
    animation: fadeInOut 2s ease-in-out;
  `;

  // Add CSS animation
  if (!document.querySelector('#sound-feedback-style')) {
    const style = document.createElement('style');
    style.id = 'sound-feedback-style';
    style.textContent = `
      @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
      }
    `;
    document.head.appendChild(style);
  }

  document.body.appendChild(feedback);

  // Remove the feedback after animation
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.parentNode.removeChild(feedback);
    }
  }, 2000);
};
