// A list of pirate sound URLs sourced from the requested soundboard.
const pirateSounds = [
  'https://www.101soundboards.com/storage/board_sounds/704738.mp3', // Pirate Sword Fight
  'https://www.101soundboards.com/storage/board_sounds/704739.mp3', // Cannon Fire
  'https://www.101soundboards.com/storage/board_sounds/704740.mp3', // Treasure Chest Open
  'https://www.101soundboards.com/storage/board_sounds/704741.mp3', // Wooden Ship Creaking
  'https://www.101soundboards.com/storage/board_sounds/704742.mp3', // Walking the Plank
  'https://www.101soundboards.com/storage/board_sounds/704743.mp3', // Parrot Squawk
  'https://www.101soundboards.com/storage/board_sounds/704744.mp3', // Pirate Grunt
  'https://www.101soundboards.com/storage/board_sounds/704745.mp3', // Ahoy <PERSON>
  'https://www.101soundboards.com/storage/board_sounds/704746.mp3', // Pirate Laugh
  'https://www.101soundboards.com/storage/board_sounds/704747.mp3', // Shivers Me Timbers
  'https://www.101soundboards.com/storage/board_sounds/704748.mp3', // Yo Ho Ho
];

// We keep track of the currently playing audio to allow for overlapping sounds if desired.
let currentAudio: HTMLAudioElement | null = null;

export const playRandomPirateSound = () => {
  const randomIndex = Math.floor(Math.random() * pirateSounds.length);
  const soundUrl = pirateSounds[randomIndex];
  
  try {
    // Create a new Audio object for each sound playback.
    // This allows sounds to overlap, which is more fun for kids.
    const audio = new Audio(soundUrl);
    
    audio.play().catch(error => {
      // The play() request was interrupted or failed.
      // This can happen if the user clicks very fast.
      // We can safely ignore these errors in this context.
      console.warn("Sound playback error:", error);
    });

  } catch (e) {
    console.error("Failed to play audio:", e);
  }
};
