// A list of pirate sound URLs sourced from the Pirate SFX Library soundboard.
const pirateSounds = [
  'https://www.101soundboards.com/storage/board_sounds/27278991.mp3', // Cannon Fire Single Shot
  'https://www.101soundboards.com/storage/board_sounds/27421388.mp3', // Ah Me Bones Are Tired
  'https://www.101soundboards.com/storage/board_sounds/27577787.mp3', // Cartoon Pirate Cheer 1
  'https://www.101soundboards.com/storage/board_sounds/27277437.mp3', // Cartoon Pirate Cheer 4
  'https://www.101soundboards.com/storage/board_sounds/27336936.mp3', // Coin Spin
  'https://www.101soundboards.com/storage/board_sounds/27439723.mp3', // Coins Drop
  'https://www.101soundboards.com/storage/board_sounds/27385961.mp3', // Hang Em From The Yard Arm
  'https://www.101soundboards.com/storage/board_sounds/27359278.mp3', // Heave Ho
  'https://www.101soundboards.com/storage/board_sounds/27231181.mp3', // Ahoy Me Hearties
  'https://www.101soundboards.com/storage/board_sounds/27331586.mp3', // Argh
  'https://www.101soundboards.com/storage/board_sounds/27634204.mp3', // Land Ho
  'https://www.101soundboards.com/storage/board_sounds/27591884.mp3', // Walk The Plank
  'https://www.101soundboards.com/storage/board_sounds/27506407.mp3', // Pirate Belly Laugh
  'https://www.101soundboards.com/storage/board_sounds/27429037.mp3', // Shiver Me Timbers
  'https://www.101soundboards.com/storage/board_sounds/27300658.mp3', // Aye Matie
  'https://www.101soundboards.com/storage/board_sounds/27551195.mp3', // Avast Ye Scallywags
  'https://www.101soundboards.com/storage/board_sounds/27617475.mp3', // Where's Me Rum
  'https://www.101soundboards.com/storage/board_sounds/27270061.mp3', // Take Him Down Below
  'https://www.101soundboards.com/storage/board_sounds/27469203.mp3', // Try Again Laddy
  'https://www.101soundboards.com/storage/board_sounds/27585217.mp3', // Young Aye Matie
];

// We keep track of the currently playing audio to allow for overlapping sounds if desired.
let currentAudio: HTMLAudioElement | null = null;

export const playRandomPirateSound = () => {
  const randomIndex = Math.floor(Math.random() * pirateSounds.length);
  const soundUrl = pirateSounds[randomIndex];
  
  try {
    // Create a new Audio object for each sound playback.
    // This allows sounds to overlap, which is more fun for kids.
    const audio = new Audio(soundUrl);
    
    audio.play().catch(error => {
      // The play() request was interrupted or failed.
      // This can happen if the user clicks very fast.
      // We can safely ignore these errors in this context.
      console.warn("Sound playback error:", error);
    });

  } catch (e) {
    console.error("Failed to play audio:", e);
  }
};
