
import React from 'react';

interface PirateButtonProps {
  onClick: () => void;
  isAnimating: boolean;
}

const PirateButton: React.FC<PirateButtonProps> = ({ onClick, isAnimating }) => {
  // Replaced the broken imgur link with a stable URL for the skull image.
  const skullImageUrl = 'https://lh3.googleusercontent.com/Zd02aNSEeEdGt0u3pU4ymZzpqCGaMt2l8m310xkKHC_LKoje4msmTqdfYetJXMCVrB70z7BhI16cGHtVxe_6FvqIrk56dWEK-3RJJsSQ0iV1WB0hBwXOIEZu1kdtv41IxuwfCF2A36E=w1280';

  return (
    <button
      onClick={onClick}
      aria-label="Play pirate sound"
      className={`transition-transform duration-150 ease-in-out transform focus:outline-none focus:ring-4 focus:ring-offset-8 focus:ring-offset-transparent focus:ring-cyan-300 focus:ring-opacity-60 rounded-full ${
        isAnimating ? 'animate-pulse scale-95' : 'hover:scale-105'
      } active:scale-90`}
    >
      <img
        src={skullImageUrl}
        alt="Colorful skull and crossbones"
        className="w-48 h-48 md:w-64 md:h-64 object-contain drop-shadow-2xl"
      />
    </button>
  );
};

export default PirateButton;
