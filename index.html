<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Captain <PERSON><PERSON>'s Pirate Engine</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      @import url('https://fonts.cdnfonts.com/css/battlesbridge');
      .font-battlesbridge {
        font-family: 'Battlesbridge';
      }
      .rainbow-bg {
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
      }
      @keyframes gradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
    </style>
    <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="rainbow-bg">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>